<template>
	<tm-app ref="app">
		<view class="main">
			<!-- 地图区域 -->
			<view class="map-container" :class="clockStatus == 1?'map-container-dk':''">
				<map class="map" :latitude="mapCenterLat" :longitude="mapCenterLng" :markers="markers" :scale="mapScale"></map>
				<!-- <map class="map" :latitude="customerLat || latitude" :longitude="customerLng || longitude" :markers="markers" :scale="mapScale"></map> -->
			</view>

			<view class="info-container" v-if="clockStatus == 1">
				<view class="info-item">
					<view class="label">地址：</view>
					<view class="value">{{ address }}</view>
				</view>
				<view class="info-item">
					<view class="label">时间：</view>
					<view class="value">{{ currentTime }}</view>
				</view>
				<view class="info-item">
					<view class="label">客户：</view>
					<view class="value">{{ customerName }}</view>
				</view>
				<view class="info-item">
					<view class="label">距离：</view>
					<view class="value">{{ distanceToCustomer }}</view>
				</view>

				<view class="box" @click="handleClock" v-if="clockFlowData.isClockIn == 0||clockFlowData.isClockIn == 1">
					<view class="boxcontent">
						<tm-icon name="tmicon-position-fill" :font-size="50" color="#fff"></tm-icon>
						<view class="text1">上户打卡</view>
					</view>
				</view>
				<view class="box" @click="handleClock" v-if="clockFlowData.isClockIn == 9&&clockFlowData.isClockOut == 0">
					<view class="boxcontent">
						<tm-icon name="tmicon-position-fill" :font-size="50" color="#fff"></tm-icon>
						<view class="text1">下户打卡</view>
					</view>
				</view>
				<view class="submit-button" @click="handleClockTemp" v-if="clockFlowData.isClockIn == 0">在医院打卡</view>
				<view class="submit-button"
				 @click="goLink('/pages/clock_in/index')" 
				 v-if="clockFlowData.isClockIn == 2"
				 >补全上户流程</view>
				<view class="submit-button" 
				 @click="goLink('/pages/clock_out/index')"
				 v-if="clockFlowData.isClockOut == 2"
				>补全下户流程</view>
			</view>

			<view class="info-container" v-if="clockStatus == 2">
				<view class="no-order">
					<text>今日无单</text>
					<text>无需打卡</text>
				</view>
				<view class="no-order-text">
					<text>如上户日期有变化</text>
					<text>请先联系派单老师修改合同上工时间</text>
				</view>
				<view class="button_area">
					<view class="button" @click="goLink('/pages/jdb/index')">抢单大厅 找工作</view>
					<view class="button button2" @click="goLink('/pages/info_schedule/index')">我的合同档期</view>
				</view>
			</view>

			<view class="info-container" v-if="clockStatus == 3">
				<view class="no-order">
					<text>今日无单</text>
					<text>无需打卡</text>
				</view>
				<view class="no-order-text">
					<text>您今日下户已打卡</text>
					<text>打卡时间：{{ clockFlowData.clockOutFlow.date }}</text>
				</view>
				<view class="button_area">
					<view class="button" @click="goLink('/pages/jdb/index')">抢单大厅 找工作</view>
					<view class="button button2" @click="goLink('/pages/info_schedule/index')">我的合同档期</view>
				</view>
			</view>

			<!-- <view class="info-container completed-box" v-if="clockStatus == 3">
				<view class="status-header">
					<tm-icon name="tmicon-check-circle" color="#4CAF50" :font-size="60"></tm-icon>
					<text class="status-title">您今日的下户打卡已完成</text>
				</view>
				
				<view class="detail-card">
					<view class="detail-item">
						<text class="item-label">客户姓名</text>
						<text class="item-value">{{customerName}}</text>
					</view>
					<view class="detail-item">
						<text class="item-label">打卡时间</text>
						<text class="item-value address">{{currentTime}}</text>
					</view>
					<view class="detail-item">
						<text class="item-label">服务地址</text>
						<text class="item-value address">{{address}}</text>
					</view>
				</view>
			</view> -->

		</view>
		<canvas 
			id="avatarCanvas" 
			type="2d"
			style="width: 70rpx; height: 70rpx; position: fixed; left: -9999rpx;"
		></canvas>
		<tm-overlay v-model:show="showWin" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
			<view class="dkpopup">
				<image src="/static/img/dktc.png" mode="widthFix" class="dkcg"></image>
				<view class="title">打卡成功</view>
				<image src="/static/img/right2.png" mode="widthFix" class="right2"></image>
				<view class="content">
					<text class="content-text">您在医院临时打卡成功</text>
					<text class="content-text">请到客户家后，补全打卡流程</text>
				</view>
				<view class="confirm" @click="showWin = false">确认</view>
			</view>
		</tm-overlay>
		<tm-overlay v-model:show="showWin3" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
			<view class="dkpopup">
				<image src="/static/img/dktc.png" mode="widthFix" class="dkcg"></image>
				<view class="title">打卡成功</view>
				<view class="content content2">
					<text class="content-text">{{ clockInMsg }}</text>
				</view>
				<view class="confirm" @click="next">确认</view>
			</view>
		</tm-overlay>
		<tm-overlay v-model:show="showWin2" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
			<view class="dkpopup">
				<image src="/static/img/dktc.png" mode="widthFix" class="dkcg"></image>
				<view class="title">下户提醒</view>
				<view class="content content2">
					<text class="content-text">系统检测到与原定下户日期误差超过24小时，请联系皖嫂派单老师，确认和修改下户时间！</text>
					<text class="content-text">您确认现在下户吗？</text>
				</view>
				<view class="button_area">
					<view class="button">不下户</view>
					<view class="button button2">下户</view>
				</view>
			</view>
		</tm-overlay>
	</tm-app>
</template>

<script lang="ts" setup>
/**************************** 依赖引入 ****************************/
import { ref, computed, watch, getCurrentInstance, onMounted } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'

/**************************** 分享功能 ****************************/
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// setTimeout(() => {
// 	clockStatus.value = 3
// }, 1000);
/**************************** 页面数据 ****************************/
const store = useStore()
// 响应式数据
const avatar = computed(() => store.userInfo.photo)
const latitude = ref<number>(0)
const longitude = ref<number>(0)
const address = ref('')
const currentTime = ref('')
const customerName = ref('')
const showWin = ref(false)
const showWin2 = ref(false)
const showWin3 = ref(false)
const clockStatus = ref(1)
const markers = ref<Array<{
	id: number;
	latitude: number;
	longitude: number;
	iconPath?: string;
	width?: number;
	height?: number;
	anchor?: { x: number; y: number };
}>>([])
const mapScale = ref(14)
const isProcessing = ref(false)
const lastProcessedAvatar = ref('')

// 在响应式数据区域添加客户坐标
const customerLat = ref<number>(0)
const customerLng = ref<number>(0)

// 在响应式数据区域添加距离变量
const distanceToCustomer = ref('计算中...');

// 在响应式数据区域添加首次计算标志
const isFirstDistanceUpdate = ref(true)

// 在响应式数据区域添加地图中心点坐标（在其他 ref 变量附近添加）
const mapCenterLat = ref<number>(31.78671) // 设置一个默认值，比如合肥市中心
const mapCenterLng = ref<number>(117.222405)

/**************************** 合同数据 ****************************/
interface ContractDetail {
	cid: string;
	hid: string;
	serial_number: string;
	uname: string;
	address: string;
	longitude: string;
	latitude: string;
	qrcode_clock_in: string;
	qrcode_clock_out: string;
}

// 在响应式数据区域添加合同数据
const contractDetail = ref<ContractDetail>({
	cid: '',
	hid: '',
	serial_number: '',
	uname: '',
	address: '',
	longitude: '',
	latitude: '',
	qrcode_clock_in: '',
	qrcode_clock_out: ''
})

/**************************** 打卡流程数据 ****************************/
interface ClockFlow {
	flow: number;
	title: string;
	date: string;
}

interface ClockFlowData {
	isClockIn: number;
	isClockOut: number;
	clockInFlow: ClockFlow;
	clockOutFlow: ClockFlow;
}

const clockFlowData = ref<ClockFlowData>({
	isClockIn: 0,
	isClockOut: 0,
	clockInFlow: { flow: 0, title: '', date: '' },
	clockOutFlow: { flow: 0, title: '', date: '' }
})

/**************************** 工具函数 ****************************/
// 处理用户头像生成地图标记
const processAvatarImage = async (avatarUrl: string): Promise<string> => {
	// 如果正在处理，等待一小段时间后重试
	if (isProcessing.value) {
		await new Promise(resolve => setTimeout(resolve, 100))
		return processAvatarImage(avatarUrl)
	}
	
	isProcessing.value = true
	
	try {
		return await new Promise((resolve, reject) => {
			const query = uni.createSelectorQuery()
			query.select('#avatarCanvas')
				.fields({ node: true, size: true })
				.exec(async (res) => {
					// 增加canvas元素检查
					if (!res[0] || !res[0].node) {
						reject(new Error('Canvas element not found'))
						return
					}
					
					const canvas = res[0].node
					const ctx = canvas.getContext('2d')
					
					// 增加canvas宽高重置逻辑
					canvas.width = 0
					canvas.height = 0
					
					// 初始化画布大小
					const systemInfo = uni.getSystemInfoSync()
					const dpr = systemInfo.pixelRatio
					canvas.width = 70 * dpr
					canvas.height = 70 * dpr
					ctx.scale(dpr, dpr)
					
					// 加载头像图片
					const img = canvas.createImage()
					img.crossOrigin = 'anonymous' // 增加跨域属性
					img.onload = async () => {
						// 清空画布
						ctx.clearRect(0, 0, 70, 70)
						
						// 绘制淡蓝色阴影圆圈（直径是头像的两倍）
						ctx.beginPath()
						ctx.arc(35, 35, 35, 0, Math.PI * 2)
						ctx.fillStyle = 'rgba(0, 122, 255, 0.15)'
						ctx.fill()
						
						// 绘制蓝色边框
						ctx.beginPath()
						ctx.arc(35, 35, 17.5, 0, Math.PI * 2)
						ctx.strokeStyle = '#007AFF'
						ctx.lineWidth = 2
						ctx.stroke()
						
						// 创建圆形裁剪区域
						ctx.beginPath()
						ctx.arc(35, 35, 16.5, 0, Math.PI * 2)
						ctx.clip()
						
						// 绘制头像（缩小尺寸）
						ctx.drawImage(img, 18.5, 18.5, 33, 33)
						
						// 增加延时确保渲染完成
						await new Promise(resolve => setTimeout(resolve, 50))
						
						// 输出为临时文件
						uni.canvasToTempFilePath({
							canvas,
							fileType: 'png', // 明确指定文件类型
							quality: 1,
							success: res => {
								// 修改路径验证逻辑
								if (res.tempFilePath && (res.tempFilePath.includes('tmp/') || res.tempFilePath.startsWith('wxfile://'))) {
									resolve(res.tempFilePath)
								} else {
									console.error('生成的临时路径异常:', res.tempFilePath)
									reject(new Error('Invalid temp file path'))
								}
							},
							fail: reject
						})
					}
					img.onerror = reject
					img.src = avatarUrl
				})
		})
	} catch (err) {
		console.error('图片处理失败:', err)
		// 确保默认图片路径正确
		return '/static/img/blue.png' 
	} finally {
		isProcessing.value = false
	}
}

// 获取当前位置信息
const getCurrentLocation = () => {
	return new Promise((resolve, reject) => {
		uni.getLocation({
			type: 'gcj02',
			success: (res) => {
				latitude.value = Number(res.latitude)
				longitude.value = Number(res.longitude)
				console.log(res.latitude,res.longitude)
				
				// latitude.value = 31.78671
				// longitude.value = 117.221516
				// 更新地图中心点
				setTimeout(() => {
					updateMapCenter()
					resolve()
				}, 1000);
			},
			fail: (err) => {
				uni.showToast({
					title: '获取位置失败',
					icon: 'none'
				})
				reject(err)
			},
		})
	})
}

// 更新时间显示
const updateTime = () => {
	const now = new Date()
	const year = now.getFullYear()
	const month = now.getMonth() + 1
	const day = now.getDate()
	const hours = now.getHours().toString().padStart(2, '0')
	const minutes = now.getMinutes().toString().padStart(2, '0')
	const seconds = now.getSeconds().toString().padStart(2, '0')
	currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 在工具函数区域添加坐标转换函数
const transformWGS84ToGCJ02 = (wgLat: number, wgLng: number): { lat: number; lng: number } => {
	const a = 6378245.0;
	const ee = 0.00669342162296594323;
	
	const isInChina = (lat: number, lng: number) => {
		return (lng >= 72.004 && lng <= 137.8347) && (lat >= 0.8293 && lat <= 55.8271);
	};

	const transformLat = (x: number, y: number) => {
		let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin(y / 3.0 * Math.PI)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(y / 12.0 * Math.PI) + 320 * Math.sin(y * Math.PI / 30.0)) * 2.0 / 3.0;
		return ret;
	};

	const transformLng = (x: number, y: number) => {
		let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin(x / 3.0 * Math.PI)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(x / 12.0 * Math.PI) + 300.0 * Math.sin(x / 30.0 * Math.PI)) * 2.0 / 3.0;
		return ret;
	};

	if (!isInChina(wgLat, wgLng)) {
		return { lat: wgLat, lng: wgLng };
	}

	let dLat = transformLat(wgLng - 105.0, wgLat - 35.0);
	let dLng = transformLng(wgLng - 105.0, wgLat - 35.0);
	const radLat = wgLat / 180.0 * Math.PI;
	let magic = Math.sin(radLat);
	magic = 1 - ee * magic * magic;
	const sqrtMagic = Math.sqrt(magic);
	dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Math.PI);
	dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * Math.PI);
	
	return {
		lat: wgLat + dLat,
		lng: wgLng + dLng
	};
};

// 修改adjustMapScale函数
const adjustMapScale = (distance: number) => {
	if (!isFirstDistanceUpdate.value) return
	
	// 根据距离动态调整地图缩放级别（仅首次）
	if (distance < 500) {         // 500米内
		mapScale.value = 17
	} else if (distance < 2000) { // 500米-2公里
		mapScale.value = 15
	} else if (distance < 5000) { // 2公里-5公里
		mapScale.value = 13
	} else {                      // 5公里以上
		mapScale.value = 11
	}
	
	isFirstDistanceUpdate.value = false
}

// 修改updateDistance函数
const updateDistance = () => {
	if (customerLat.value && customerLng.value && latitude.value && longitude.value) {
		const dist = calculateDistance(
			latitude.value,
			longitude.value,
			customerLat.value,
			customerLng.value
		);
		
		// 添加距离单位判断
		if (dist < 1000) {
			distanceToCustomer.value = `${Math.round(dist)}米`;
		} else {
			distanceToCustomer.value = `${(dist / 1000).toFixed(1)}公里`;
		}

		// 仅在首次计算时调整地图缩放
		adjustMapScale(dist);
		
	} else {
		distanceToCustomer.value = '--';
	}
};

// 在工具函数区域添加更新地图中心点的函数
const updateMapCenter = () => {
	if (latitude.value && longitude.value && customerLat.value && customerLng.value) {
		// 确保所有值都是数值类型
		mapCenterLat.value = Number((latitude.value + customerLat.value) / 2)
		mapCenterLng.value = Number((longitude.value + customerLng.value) / 2)
	} else if (latitude.value && longitude.value) {
		mapCenterLat.value = Number(latitude.value)
		mapCenterLng.value = Number(longitude.value)
	} else if (customerLat.value && customerLng.value) {
		mapCenterLat.value = Number(customerLat.value)
		mapCenterLng.value = Number(customerLng.value)
	}
}

// 添加新的更新标记函数
const updateMarkers = async (newAvatar?: string, newLat?: number, newLng?: number, newCLat?: number, newCLng?: number) => {
	// 1. 处理头像
	let processedAvatar = lastProcessedAvatar.value
	if (newAvatar && newAvatar !== lastProcessedAvatar.value) {
		try {
			processedAvatar = await processAvatarImage(newAvatar)
		} catch (error) {
			console.error('头像处理失败:', error)
			processedAvatar = await processAvatarImage('/static/img/blue.png')
		}
		lastProcessedAvatar.value = processedAvatar
	}

	// 2. 更新距离和地图中心点
	if (newLat !== undefined || newLng !== undefined || newCLat !== undefined || newCLng !== undefined) {
		updateDistance()
		// updateMapCenter()
	}

	// 3. 更新地图标记
	const currentLat = newLat ?? latitude.value
	const currentLng = newLng ?? longitude.value
	const currentCLat = newCLat ?? customerLat.value
	const currentCLng = newCLng ?? customerLng.value

	const newMarkers = [
		{
			id: 1,
			latitude: Number(currentLat),
			longitude: Number(currentLng),
			iconPath: processedAvatar,
			width: 60,
			height: 60,
			anchor: { x: 0.5, y: 0.5 }
		}
	]

	if (currentCLat && currentCLng) {
		newMarkers.push({
			id: 2,
			latitude: Number(currentCLat),
			longitude: Number(currentCLng),
			iconPath: '/static/img/location.png',
			width: 30,
			height: 30,
			anchor: { x: 0.5, y: 0.5 }
		})
	}

	if (JSON.stringify(markers.value) !== JSON.stringify(newMarkers)) {
		markers.value = newMarkers
	}
}

/**************************** 事件处理 ****************************/
const isSubmitting = ref(false) // 防止重复提交

// 计算两个坐标点之间的距离（米）
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
	const R = 6371e3; // 地球半径，单位米
	const φ1 = lat1 * Math.PI / 180;
	const φ2 = lat2 * Math.PI / 180;
	const Δφ = (lat2 - lat1) * Math.PI / 180;
	const Δλ = (lon2 - lon1) * Math.PI / 180;

	const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
			Math.cos(φ1) * Math.cos(φ2) *
			Math.sin(Δλ/2) * Math.sin(Δλ/2);
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

	return R * c;
};

const handleClock = () => {
	if(clockFlowData.value.isClockIn == 0||clockFlowData.value.isClockIn == 1){
		handleSubmit(1001)
	}else if(clockFlowData.value.isClockIn == 9&&clockFlowData.value.isClockOut == 0&&clockFlowData.value.clockInFlow.flow == 3){
		handleSubmit(2001)
	}
}

const handleClockTemp = () => {
	handleSubmit(1002)
}

/**************************** 生命周期 ****************************/
onLoad(async () => {
	getContractDetail()
	updateTime()
	setInterval(updateTime, 1000)
	
	try {
		await getCurrentLocation()
		// 初始化标记
		await updateMarkers(avatar.value, latitude.value, longitude.value, customerLat.value, customerLng.value)
	} catch (error) {
		console.error('初始化位置失败:', error)
	}
})
/**************************** 观察器 ****************************/
// 修改现有的watch监听器，使用新的updateMarkers函数
watch(
	[avatar, () => latitude.value, () => longitude.value, () => customerLat.value, () => customerLng.value],
	async ([newAvatar, newLat, newLng, newCLat, newCLng]) => {
		await updateMarkers(newAvatar, newLat, newLng, newCLat, newCLng)
	}
)

/**************************** 接口函数 ****************************/
const getContractDetail = async () => {
	try {
		const res = await api.request.ajax({
			url: '/work/ctDetail',
			type: 'POST',
		})
		
		if (res.code === 1) {
			contractDetail.value = res.data
			
			customerLat.value = Number(res.data.latitude)
			customerLng.value = Number(res.data.longitude)
			
			// 更新地图中心点
			updateMapCenter()

			address.value = res.data.address
			customerName.value = res.data.uname

			await getClockFlow(2)
			
		} else if(res.code === 0) {
			clockStatus.value = 2
		}
	} catch (error) {
		console.error('获取合同详情失败:', error)
		uni.showToast({
			title: '获取合同信息失败',
			icon: 'none'
		})
	}
}

// 添加获取打卡流程函数
const getClockFlow = async (params?: number) => {
	try {
		if (!contractDetail.value.cid || !contractDetail.value.hid) return
		
		const res = await api.request.ajax({
			url: '/work/clockFlow',
			type: 'POST',
			data:{
				cid: contractDetail.value.cid,
				hid: contractDetail.value.hid
			}
		})
		
		if (res.code === 1) {
			clockFlowData.value = res.data
			if(res.data.isClockIn === 9&&res.data.isClockOut === 9&&res.data.clockInFlow.flow == 3&&res.data.clockOutFlow.flow == 3) {
				clockStatus.value = 3
			}
			
			if(params===2){
				if(clockFlowData.value.isClockOut == 2){
					setTimeout(() => {
						goLink('/pages/clock_out/index')
					}, 2000);
				}
			}
		}
	} catch (error) {
		console.error('获取打卡流程失败:', error)
		uni.showToast({
			title: '获取打卡进度失败',
			icon: 'none'
		})
	}
}


// 通用打卡
let operatesave = null
const clockInMsg = ref('')
const handleSubmit = async (operate) => {
	if (isSubmitting.value) return;
	isSubmitting.value = true;
	
	try {
		// 参数校验
		if (!contractDetail.value.cid || !contractDetail.value.hid) {
			uni.showToast({ title: '合同信息不完整', icon: 'none' });
			return;
		}

		// 计算与客户位置距离
		const distance = calculateDistance(
			latitude.value,
			longitude.value,
			customerLat.value,
			customerLng.value
		);

		const res = await api.request.ajax({
			url: '/work/clockIn',
			type: 'POST',
			data:{
				cid: contractDetail.value.cid,
				operate: operate,
				latitude: latitude.value.toFixed(6),
				longitude: longitude.value.toFixed(6),
				distance: distance.toFixed(0),
				hid: contractDetail.value.hid
			}
		});

		if (res.code === 1) {
			await getClockFlow()
			clockInMsg.value = res.msg
			operatesave = operate
			if(operatesave == 1001){
				showWin3.value = true
			}else if(operatesave == 1002){
				showWin.value = true;
			}else if(operatesave == 2001){
				showWin3.value = true
			}
		} else {
			uni.showToast({ 
				title: res.msg || '打卡失败',
				icon: 'none'
			});
		}
	} catch (error) {
		console.error('打卡请求失败:', error);
		uni.showToast({ 
			title: '网络异常，请稍后重试',
			icon: 'none'
		});
	} finally {
		isSubmitting.value = false;
	}
}
const next = ()=>{
	if(operatesave == 1001){
		goLink('/pages/clock_in/index')
	}else if(operatesave == 1002){
		showWin.value = true;
	}else if(operatesave == 2001){
		goLink('/pages/clock_out/index')
	}
}
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.map-container {
		width: 100%;
		height: calc(100vh - 600rpx);
		
		.map {
			width: 100%;
			height: 100%;
		}
	}
	.map-container-dk{
		height: calc(100vh - 850rpx);
	}

	.info-container {
		width: 100%;
		padding: 30rpx;
		background-color: #fff;
		box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 1rpx 1rpx 0px 0px #FFFFFF;
		border-radius: 20rpx 26rpx 26rpx 20rpx;
		position: absolute;
		bottom: 0;

		.info-item {
			height: 100rpx;
			display: flex;
			align-items: center;
			font-size: 30rpx;
			width: 95%;
			padding: 0 20rpx;
			border-bottom: 1rpx solid #EBEBEB;
			.label {
				color: #666;
				width: 100rpx;
			}

			.value {
				color: #333;
				flex: 1;
			}
		}
		.box {
			margin-top: 40rpx;
		}

		.submit-button {
			margin-top: 20rpx;
			width: 90%;
			height: 88rpx;
			background: linear-gradient(90deg, #F31630, #FF984C);
			border-radius: 44rpx;
			color: #fff;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 40rpx;
		}
		.no-order{
			margin-top: 60rpx;
			width: 166rpx;
			height: 166rpx;
			background: #FFFFFF;
			border-radius: 50%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border: 17rpx solid #EAEAEA;
			color: #A6A6A6;
			font-size: 24rpx;
		}
		.no-order-text{
			font-size: 30rpx;
			color: #333333;
			margin-top: 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		.button_area{
			margin: 80rpx 0;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			.button{
				width: 311px;
				height: 90rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				box-shadow: 0px 0px 9px 1px rgba(236,84,64,0.41);
				border-radius: 45rpx;
				color: #fff;
				font-size: 30rpx;
				flex-wrap: wrap;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 20rpx;
			}
			.button2{
				background: linear-gradient(-15deg, #4A87F8, #58CFFD);
				box-shadow: 0px 0px 9px 1px rgba(48,139,227,0.41);
			}
		}
	}
}

.completed-box {
	background: #fff !important;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.15);

	.status-header {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 30rpx 0;
		
		.status-title {
			color: #4CAF50;
			font-size: 36rpx;
			font-weight: bold;
			margin-left: 20rpx;
		}
	}

	.detail-card {
		width: 90%;
		background: #F8F9FA;
		border-radius: 16rpx;
		padding: 30rpx;
		margin: 20rpx 0;

		.detail-item {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #eee;

			&:last-child {
				border-bottom: none;
			}

			.item-label {
				color: #666;
				font-size: 28rpx;
			}

			.item-value {
				color: #333;
				font-size: 30rpx;
				font-weight: 500;
				max-width: 400rpx;
				text-align: right;

			}
		}
	}

	.action-buttons {
		width: 100%;
		display: flex;
		justify-content: space-around;
		margin-top: 40rpx;

		.btn {
			width: 45%;
			height: 80rpx;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			transition: all 0.3s;
		}

		.record-btn {
			background: #4CAF50;
			color: #fff;
			box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);

			&:active {
				background: #43A047;
			}
		}

		.share-btn {
			border: 2rpx solid #4CAF50;
			color: #4CAF50;
			background: rgba(76, 175, 80, 0.1);

			&:active {
				background: rgba(76, 175, 80, 0.2);
			}
		}
	}
}
</style>